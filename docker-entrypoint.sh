#!/bin/bash

# Docker entrypoint script for AI Chat application

set -e

# 函数：打印带时间戳的日志
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# 函数：检查必需的环境变量
check_env_vars() {
    local required_vars=(
        "OPENAI_API_KEY"
        "OPENAI_BASE_URL"
        "API_TOKEN"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log "ERROR: Missing required environment variables:"
        printf ' - %s\n' "${missing_vars[@]}"
        exit 1
    fi
}

# 函数：等待外部服务可用
wait_for_service() {
    local service_name="$1"
    local service_url="$2"
    local max_attempts=30
    local attempt=1
    
    log "Waiting for $service_name to be available at $service_url..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s --max-time 5 "$service_url" > /dev/null 2>&1; then
            log "$service_name is available!"
            return 0
        fi
        
        log "Attempt $attempt/$max_attempts: $service_name not yet available, waiting..."
        sleep 2
        ((attempt++))
    done
    
    log "WARNING: $service_name is not available after $max_attempts attempts"
    return 1
}

# 主函数
main() {
    log "Starting AI Chat application..."
    
    # 检查环境变量
    log "Checking environment variables..."
    check_env_vars
    
    # 等待外部服务（可选，如果服务不可用也继续启动）
    if [[ -n "$OPENAI_BASE_URL" ]]; then
        wait_for_service "OpenAI API" "$OPENAI_BASE_URL" || true
    fi
    
    if [[ -n "$FASTGPT_URL" ]]; then
        wait_for_service "FastGPT" "$FASTGPT_URL" || true
    fi
    
    # 创建必要的目录
    log "Creating necessary directories..."
    mkdir -p logs
    
    # 设置权限
    log "Setting up permissions..."
    chown -R app:app /app/logs 2>/dev/null || true
    
    log "Starting the application..."
    
    # 执行传入的命令
    exec "$@"
}

# 如果脚本被直接执行，运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
