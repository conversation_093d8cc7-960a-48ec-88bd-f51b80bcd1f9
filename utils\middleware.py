import uuid, time
from loguru import logger
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware


class LogRequestMiddleware(BaseHTTPMiddleware):
    """
    自定义中间件，用于记录请求和响应的日志。
    生成一个唯一的请求ID，并在日志中记录请求开始、完成和错误信息。
    """

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        request_id = str(uuid.uuid4())  # 生成唯一的请求ID

        # 记录请求开始
        logger.info(f"[{request_id}] 开始请求 {request.method} {request.url.path}")
        # 在请求头中添加请求ID，便于后续追踪
        request.state.request_id = request_id

        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            logger.info(
                f"[{request_id}] 请求完成 {request.method} {request.url.path} - 状态码: {response.status_code}, 处理时间: {process_time:.4f}秒")
            return response
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"[{request_id}] 请求错误 {request.method} {request.url.path} - 错误: {str(e)}, 处理时间: {process_time:.4f}秒")
            raise e