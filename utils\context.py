import contextvars

# 创建上下文变量
request_id_var = contextvars.ContextVar('request_id', default='')
request_prompt_var = contextvars.ContextVar('request_prompt', default='')

def get_request_id() -> str:
    """获取当前请求ID"""
    return request_id_var.get()

def set_request_id(request_id: str) -> None:
    """设置当前请求ID"""
    request_id_var.set(request_id)


def get_request_prompt() -> str:
    """获取当前请求的提示内容"""
    return request_prompt_var.get()

def set_request_prompt(prompt: str) -> None:
    """设置当前请求的提示内容"""
    request_prompt_var.set(prompt)