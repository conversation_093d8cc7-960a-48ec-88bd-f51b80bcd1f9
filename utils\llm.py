import re
import json
import traceback
from openai import AsyncOpenAI
from config import OpenaiConfig as config
from pydantic import BaseModel
from fastmcp import Client
from ai_mcp.server import mcp
from loguru import logger
from utils.context import get_request_id, get_request_prompt
from prompt.private_chat import private_chat_system_prompt

def get_think_and_answer(content: str) -> tuple[str, str]:
    think_pattern = r'<think>(.*?)</think>'
    think_match = re.search(think_pattern, content, re.DOTALL)

    if think_match:
        # 提取reasoning_content
        reasoning_content = think_match.group(1).strip()
        # 从内容中删除<think></think>块
        answer = re.sub(think_pattern, '', content, flags=re.DOTALL).strip()
    else:
        reasoning_content = ''
        answer = content.strip()

    return reasoning_content, answer


class LLMResponse(BaseModel):
    success: bool
    reasoning_content: str
    answer: str
    prompt_tokens: int
    completion_tokens: int
    all_tokens: int
    tool_calls_used: bool = False
    tool_results: list = []



async def get_available_tools():
    """
    动态获取MCP服务器提供的工具列表

    Returns:
        list: 格式化后的工具列表，符合OpenAI工具调用格式
    """
    try:
        async with Client(mcp) as client:
            tools = await client.list_tools()

            # 转换为OpenAI工具格式（虽然我们不直接使用，但保持格式一致性）
            formatted_tools = []
            for tool in tools:
                formatted_tool = {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.inputSchema if hasattr(tool, 'inputSchema') else {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    }
                }
                formatted_tools.append(formatted_tool)

            return formatted_tools
    except Exception as e:
        traceback.print_exc()
        return []


def generate_tools_description(tools: list) -> str:
    """
    根据工具列表生成工具描述文本，用于系统提示

    Args:
        tools: 工具列表

    Returns:
        str: 工具描述文本
    """
    if not tools:
        return "当前没有可用的工具。"

    descriptions = []
    for tool in tools:
        tool_info = tool.get("function", {})
        name = tool_info.get("name", "")
        description = tool_info.get("description", "")

        # 从参数中提取必需参数
        parameters = tool_info.get("parameters", {})
        properties = parameters.get("properties", {})
        required = parameters.get("required", [])

        # 构建参数示例
        param_examples = []
        for param_name in required:
            param_info = properties.get(param_name, {})
            param_desc = param_info.get("description", param_name)
            param_examples.append(f'{param_name}="{param_desc}"')

        param_str = ", ".join(param_examples) if param_examples else ""

        descriptions.append(f"- {name}({param_str}) - {description}")

    return "\n".join(descriptions)


async def call_mcp_tool(tool_name: str, arguments: dict) -> dict:
    """
    调用MCP工具

    Args:
        tool_name: 工具名称
        arguments: 工具参数

    Returns:
        工具执行结果
    """
    try:
        async with Client(mcp) as client:
            result = await client.call_tool(tool_name, arguments)
            logger.debug(f"调用工具 {tool_name} 返回结果: {result}")
            # 处理MCP返回的TextContent对象
            if isinstance(result, list) and len(result) > 0:
                # 获取第一个TextContent对象的文本内容
                text_content = result[0]
                if hasattr(text_content, 'text'):
                    try:
                        # 尝试解析JSON
                        parsed_result = json.loads(text_content.text)
                        return parsed_result
                    except json.JSONDecodeError:
                        # 如果不是JSON，直接返回文本
                        return {"status": "success", "data": text_content.text}
                else:
                    return {"status": "success", "data": str(text_content)}
            else:
                return {"status": "success", "data": result}

    except Exception as e:
        traceback.print_exc()
        return {"status": "error", "message": f"工具调用失败: {str(e)}"}


def get_client() -> AsyncOpenAI:
    """
    获取异步OpenAI客户端实例。
    该函数从配置文件中读取API密钥和基础URL，并返回一个AsyncOpenAI客户端实例。
    """
    client = AsyncOpenAI(
        api_key=config.OPENAI_API_KEY,  # 从配置文件读取API密钥
        base_url=config.OPENAI_BASE_URL  # 从配置文件读取基础URL
    )
    return client


def parse_tool_calls_from_text(text: str) -> list:
    """
    从文本中解析工具调用请求
    格式: <tool_call>function_name(arg1="value1", arg2="value2")</tool_call>
    """
    import re
    tool_calls = []

    # 匹配工具调用模式
    pattern = r'<tool_call>(\w+)\((.*?)\)</tool_call>'
    matches = re.findall(pattern, text, re.DOTALL)

    for function_name, args_str in matches:
        try:
            # 解析参数
            args = {}
            if args_str.strip():
                # 简单的参数解析 (支持 key="value" 格式)
                arg_pattern = r'(\w+)="([^"]*)"'
                arg_matches = re.findall(arg_pattern, args_str)
                for key, value in arg_matches:
                    args[key] = value

            tool_calls.append({
                "function_name": function_name,
                "arguments": args
            })
        except Exception as e:
            traceback.print_exc()

    return tool_calls


async def get_llm_response(messages: list) -> LLMResponse:
    """
    使用指定的模型和消息列表获取LLM响应，支持基于文本的工具调用。
    """
    request_id = get_request_id()
    request_prompt = get_request_prompt()
    try:
        # 获取异步OpenAI客户端
        client = get_client()

        # 动态获取可用工具
        available_tools = await get_available_tools()
        tools_description = generate_tools_description(available_tools)

        # 添加系统提示，告诉AI如何使用工具
        if messages[0].role != "system":
            system_prompt = private_chat_system_prompt.replace("<tools>", tools_description)
            messages.insert(0, {"role": "system", "content": system_prompt})

        # 添加用户提示
        if request_prompt:
            messages[0].update({
                "content": f"{messages[0]['content']}\n\n{request_prompt}"
            })


        logger.debug(f"[{request_id}]{'='*50}")
        logger.debug(f"[{request_id}]{messages}")
        logger.debug(f"[{request_id}]{'='*50}")
        # 第一次调用LLM
        response = await client.chat.completions.create(
            model=config.OPENAI_MODEL,
            messages=messages,
            temperature=config.TEMPERATURE,
            max_tokens=config.MAX_TOKENS,
            stream=False
        )

        total_prompt_tokens = response.usage.prompt_tokens
        total_completion_tokens = response.usage.completion_tokens
        total_tokens = response.usage.total_tokens
        tool_results = []
        tool_calls_used = False

        if response and response.choices:
            content = response.choices[0].message.content.strip()
            logger.debug(f"[{request_id}]LLM响应内容: {content}")
            # 检查是否包含工具调用
            tool_calls = parse_tool_calls_from_text(content)
            logger.debug(f"[{request_id}]解析到工具调用: {tool_calls}")

            if tool_calls:
                tool_calls_used = True
                logger.info(f"[{request_id}]检测到 {len(tool_calls)} 个工具调用请求")

                # 执行工具调用
                tool_results_text = []
                for tool_call in tool_calls:
                    function_name = tool_call["function_name"]
                    function_args = tool_call["arguments"]

                    logger.debug(f"[{request_id}]执行工具调用: {function_name} with args: {function_args}")

                    # 调用MCP工具
                    tool_result = await call_mcp_tool(function_name, function_args)
                    tool_results.append({
                        "tool": function_name,
                        "args": function_args,
                        "result": tool_result
                    })
                    logger.debug(f"[{request_id}]工具 {function_name} 执行结果: {tool_result}")
                    # 格式化工具结果
                    result_text = f"工具 {function_name} 的执行结果：\n{json.dumps(tool_result, ensure_ascii=False, indent=2)}"
                    tool_results_text.append(result_text)

                # 添加工具结果到对话历史
                messages.append({
                    "role": "assistant",
                    "content": content
                })
                messages.append({
                    "role": "user",
                    "content": f"工具执行结果：\n\n{chr(10).join(tool_results_text)}\n\n请基于以上工具执行结果，为用户提供完整的回答。"
                })

                # 再次调用LLM生成最终回答
                logger.info(f"[{request_id}]基于工具结果生成最终回答")
                final_response = await client.chat.completions.create(
                    model=config.OPENAI_MODEL,
                    messages=messages,
                    temperature=config.TEMPERATURE,
                    max_tokens=config.MAX_TOKENS,
                    stream=False
                )

                # 累加token使用量
                total_prompt_tokens += final_response.usage.prompt_tokens
                total_completion_tokens += final_response.usage.completion_tokens
                total_tokens += final_response.usage.total_tokens

                final_content = final_response.choices[0].message.content.strip()
                logger.debug(f"[{request_id}]使用工具，最终回答内容: {final_content}")
            else:
                # 没有工具调用，直接使用原始回答
                final_content = content
                logger.debug(f"[{request_id}]未使用工具，最终回答内容: {final_content}")

            if final_content:
                reasoning_content, answer = get_think_and_answer(final_content)

                result = LLMResponse(
                    success=True,
                    reasoning_content=reasoning_content,
                    answer=answer,
                    prompt_tokens=total_prompt_tokens,
                    completion_tokens=total_completion_tokens,
                    all_tokens=total_tokens,
                    tool_calls_used=tool_calls_used,
                    tool_results=tool_results
                )
            else:
                result = LLMResponse(
                    success=False,
                    reasoning_content="",
                    answer="No response from the model.",
                    prompt_tokens=total_prompt_tokens,
                    completion_tokens=total_completion_tokens,
                    all_tokens=total_tokens,
                    tool_calls_used=tool_calls_used,
                    tool_results=tool_results
                )
        else:
            result = LLMResponse(
                success=False,
                reasoning_content="",
                answer="No response from the model.",
                prompt_tokens=0,
                completion_tokens=0,
                all_tokens=0,
                tool_calls_used=False,
                tool_results=[]
            )

    except Exception as e:
        traceback.print_exc()
        logger.error(f"[{request_id}]LLM响应生成错误: {str(e)}")
        result = LLMResponse(
            success=False,
            reasoning_content="",
            answer=f"Error: {str(e)}",
            prompt_tokens=0,
            completion_tokens=0,
            all_tokens=0,
            tool_calls_used=False,
            tool_results=[]
        )

    return result