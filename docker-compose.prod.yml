# Production Docker Compose Override
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  ai-chat:
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    
    # 生产环境健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 15s
      timeout: 5s
      retries: 5
      start_period: 30s
    
    # 生产环境环境变量覆盖
    environment:
      - LOG_LEVEL=INFO  # 生产环境使用 INFO 级别
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    # 生产环境安全配置
    security_opt:
      - no-new-privileges:true
    
    # 只读根文件系统（除了必要的写入目录）
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp
    
    # 用户权限
    user: "1000:1000"  # 使用非 root 用户

# 生产环境网络配置
networks:
  ai-chat-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
