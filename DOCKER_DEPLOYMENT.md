# AI Chat Docker 部署指南

本指南将帮助您使用 Docker 和 Docker Compose 部署 AI Chat 智能客服系统。

## 前置要求

- Docker Engine 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用内存
- 至少 1GB 可用磁盘空间

## 快速开始

### 1. 克隆项目（如果还没有）

```bash
git clone <your-repo-url>
cd ai-chat
```

### 2. 配置环境变量

复制环境变量模板并根据您的需求进行配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，更新以下关键配置：

```bash
# 必须配置的项目
OPENAI_API_KEY=your-actual-api-key
OPENAI_BASE_URL=your-openai-compatible-endpoint
API_TOKEN=your-secure-api-token

# 可选配置
FASTGPT_DATASET_ID=your-dataset-id
FASTGPT_TOKEN=your-fastgpt-token
```

### 3. 构建和启动服务

```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f ai-chat

# 检查服务状态
docker-compose ps
```

### 4. 验证部署

访问以下 URL 验证服务是否正常运行：

- 应用首页: http://localhost:31888
- 健康检查: http://localhost:31888/
- API 文档: http://localhost:31888/docs

## 详细配置

### 环境变量说明

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | OpenAI API 密钥 | - | ✅ |
| `OPENAI_BASE_URL` | OpenAI API 基础URL | - | ✅ |
| `OPENAI_MODEL` | 使用的模型名称 | qwen32b | ❌ |
| `API_TOKEN` | API 访问令牌 | - | ✅ |
| `API_PORT` | 容器内部端口 | 8000 | ❌ |
| `LOG_LEVEL` | 日志级别 | INFO | ❌ |

### 端口配置

默认端口映射：
- 主机端口: `31888`
- 容器端口: `8000`

如需修改主机端口，编辑 `docker-compose.yml` 中的 `ports` 配置：

```yaml
ports:
  - "your-port:8000"
```

### 数据持久化

日志文件会自动持久化到主机的 `./logs` 目录。

## 常用命令

### 服务管理

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f ai-chat
```

### 更新部署

```bash
# 重新构建镜像
docker-compose build --no-cache

# 重新部署
docker-compose up -d --force-recreate
```

### 调试

```bash
# 进入容器
docker-compose exec ai-chat bash

# 查看容器资源使用情况
docker stats ai-chat-app

# 查看详细日志
docker-compose logs --tail=100 ai-chat
```

## 故障排除

### 常见问题

1. **容器启动失败**
   - 检查环境变量配置
   - 查看容器日志: `docker-compose logs ai-chat`
   - 确认端口未被占用

2. **API 调用失败**
   - 验证 `OPENAI_API_KEY` 和 `OPENAI_BASE_URL` 配置
   - 检查网络连接
   - 确认外部服务可访问性

3. **权限问题**
   - 确保 `logs` 目录有写权限
   - 检查 Docker 用户权限

### 健康检查

服务包含内置健康检查，每30秒检查一次服务状态：

```bash
# 查看健康状态
docker-compose ps
```

### 性能优化

1. **内存限制**
   ```yaml
   deploy:
     resources:
       limits:
         memory: 2G
       reservations:
         memory: 1G
   ```

2. **日志轮转**
   ```yaml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```

## 生产环境建议

1. **安全配置**
   - 使用强密码和令牌
   - 配置防火墙规则
   - 定期更新镜像

2. **监控**
   - 配置日志收集
   - 设置资源监控
   - 配置告警机制

3. **备份**
   - 定期备份配置文件
   - 备份重要日志
   - 备份环境变量

## 支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查项目日志
3. 提交 Issue 并附上相关日志信息
