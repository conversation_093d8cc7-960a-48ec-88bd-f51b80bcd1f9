import json
import tiktoken
from loguru import logger
from typing import List, Dict, Any


# 初始化 tiktoken 编码器缓存
_encoders = {}


# 获取指定模型的编码器
def get_encoder(model: str):
    if model not in _encoders:
        try:
            # 尝试直接获取模型编码器
            _encoders[model] = tiktoken.encoding_for_model(model)
        except KeyError:
            # 如果模型不支持，使用通用编码器
            _encoders[model] = tiktoken.get_encoding("cl100k_base")
    return _encoders[model]


# 计算字符串的token数量
def count_tokens(text: str, model: str) -> int:
    if not text:
        return 0
    encoder = get_encoder(model)
    tokens = encoder.encode(text)
    return len(tokens)


# 计算消息列表的token数量
def count_message_tokens(messages: List[Dict[str, Any]], model: str) -> int:
    token_count = 0

    # 为每个消息添加基础token
    for message in messages:
        # 每条消息增加额外的token用于格式化
        token_count += 4

        for key, value in message.items():
            # 对每个内容计算token
            if isinstance(value, str):
                token_count += count_tokens(value, model)
            elif isinstance(value, list) and key == "content":
                # 处理结构化内容
                for item in value:
                    if isinstance(item, dict) and "text" in item and isinstance(item["text"], str):
                        token_count += count_tokens(item["text"], model)
            elif isinstance(value, dict):
                # 对字典内容进行序列化计数
                json_str = json.dumps(value)
                token_count += count_tokens(json_str, model)

    # 添加额外的格式化token
    token_count += 2

    return token_count