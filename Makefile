# AI Chat Docker Management Makefile

.PHONY: help build up down restart logs status clean rebuild health test

# Default target
help:
	@echo "AI Chat Docker Management Commands:"
	@echo ""
	@echo "  build     - Build the Docker image"
	@echo "  up        - Start the services"
	@echo "  down      - Stop the services"
	@echo "  restart   - Restart the services"
	@echo "  logs      - Show service logs"
	@echo "  status    - Show service status"
	@echo "  health    - Check service health"
	@echo "  clean     - Clean up containers and images"
	@echo "  rebuild   - Rebuild and restart services"
	@echo "  test      - Test the API endpoints"
	@echo "  shell     - Open shell in the container"
	@echo ""

# Build the Docker image
build:
	@echo "Building Docker image..."
	docker-compose build

# Start services
up:
	@echo "Starting services..."
	docker-compose up -d
	@echo "Services started. Check status with 'make status'"

# Stop services
down:
	@echo "Stopping services..."
	docker-compose down

# Restart services
restart:
	@echo "Restarting services..."
	docker-compose restart

# Show logs
logs:
	@echo "Showing logs (Ctrl+C to exit)..."
	docker-compose logs -f ai-chat

# Show service status
status:
	@echo "Service status:"
	docker-compose ps

# Check health
health:
	@echo "Checking service health..."
	@curl -f http://localhost:31888/ && echo "✅ Service is healthy" || echo "❌ Service is not responding"

# Clean up
clean:
	@echo "Cleaning up containers and images..."
	docker-compose down --rmi all --volumes --remove-orphans

# Rebuild and restart
rebuild:
	@echo "Rebuilding and restarting services..."
	docker-compose down
	docker-compose build --no-cache
	docker-compose up -d
	@echo "Services rebuilt and restarted"

# Test API endpoints
test:
	@echo "Testing API endpoints..."
	@echo "Testing root endpoint..."
	@curl -s http://localhost:31888/ > /dev/null && echo "✅ Root endpoint OK" || echo "❌ Root endpoint failed"
	@echo "Testing docs endpoint..."
	@curl -s http://localhost:31888/docs > /dev/null && echo "✅ Docs endpoint OK" || echo "❌ Docs endpoint failed"

# Open shell in container
shell:
	@echo "Opening shell in container..."
	docker-compose exec ai-chat bash
