from fastmcp import FastMCP
from typing import Optional, Dict, Any
import sys
import os

# 添加项目根目录到系统路径，确保可以导入 utils 模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_mcp.query import (
    get_products,
    get_info_by_id,
    get_stock_by_id,
    get_price_by_id
)

# 创建 FastMCP 应用实例
mcp = FastMCP(
    name="Product Query MCP Server",
    description="用于查询产品信息、库存和价格的MCP服务器",
    version="1.0.0",
)


@mcp.tool
def search_products(name: str) -> Dict[str, Any]:
    """
    通过关键词进行模糊查询产品信息

    Args:
        name: 搜索关键词

    Returns:
        包含产品搜索结果的字典
    """
    try:
        result = get_products(name)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询产品信息时发生错误: {str(e)}"}


@mcp.tool
def get_product_info_by_id(product_id: str) -> Dict[str, Any]:
    """
    根据产品ID查询产品详细信息

    Args:
        product_id: 产品ID

    Returns:
        包含产品详细信息的字典
    """
    try:
        result = get_info_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询产品信息时发生错误: {str(e)}"}


@mcp.tool
def get_product_stock_by_id(product_id: str) -> Dict[str, Any]:
    """
    根据产品ID查询产品的库存信息

    Args:
        product_id: 产品ID

    Returns:
        包含产品库存信息的字典
    """
    try:
        result = get_stock_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询库存信息时发生错误: {str(e)}"}


@mcp.tool
def get_product_price_by_id(product_id: str) -> Dict[str, Any]:
    """
    根据产品ID查询产品的价格信息

    Args:
        product_id: 产品ID

    Returns:
        包含产品价格信息的字典
    """
    try:
        result = get_price_by_id(product_id)
        if "status" in result and result["status"] == "error":
            return {"status": "error", "message": result.get("message", "查询失败")}
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"查询价格信息时发生错误: {str(e)}"}


@mcp.tool
def get_product_full_info(product_id: str) -> Dict[str, Any]:
    """
    根据产品ID获取产品的完整信息（包括详情、价格、库存）

    Args:
        product_id: 产品ID

    Returns:
        包含产品完整信息的字典
    """
    try:
        # 获取产品详情
        info_result = get_info_by_id(product_id)
        if "status" in info_result and info_result["status"] == "error":
            return {"status": "error", "message": f"获取产品详情失败: {info_result.get('message', '未知错误')}"}

        # 获取价格信息
        price_result = get_price_by_id(product_id)
        if "status" in price_result and price_result["status"] == "error":
            price_data = {"error": price_result.get("message", "价格查询失败")}
        else:
            price_data = price_result.get("data", {})

        # 获取库存信息
        stock_result = get_stock_by_id(product_id)
        if "status" in stock_result and stock_result["status"] == "error":
            stock_data = {"error": stock_result.get("message", "库存查询失败")}
        else:
            stock_data = stock_result.get("data", {})

        # 合并所有信息
        full_info = {
            "product_info": info_result.get("data", {}),
            "price_info": price_data,
            "stock_info": stock_data
        }

        return {"status": "success", "data": full_info}
    except Exception as e:
        return {"status": "error", "message": f"查询完整产品信息时发生错误: {str(e)}"}

