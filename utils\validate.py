from typing import Optional
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi import  status
from config import APIConfig
security = HTTPBearer()


class CustomHTTPException(HTTPException):
    def __init__(self,
                 code: int = status.HTTP_400_BAD_REQUEST,
                 message: str = "Bad Request",
                 data: Optional[dict] = None,
                 ):
        super().__init__(status_code=code, detail=message)
        self.code = code
        self.message = message
        self.data = data or {}



def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if credentials.credentials != APIConfig.API_TOKEN:
        raise CustomHTTPException(code=status.HTTP_401_UNAUTHORIZED, message="token错误", data={})
    return credentials