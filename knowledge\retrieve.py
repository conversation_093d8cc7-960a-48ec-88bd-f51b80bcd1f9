import json
import aiohttp
import asyncio
from config import FastGPTConfig


async def retrieve_knowledge(question, limit=256, similarity=0, searchMode="embedding",
                        usingReRank=False) -> list:
    """
    知识库内容检索
    """
    url = FastGPTConfig.FASTGPT_URL
    headers = {
        'Authorization': f'Bearer {FastGPTConfig.FASTGPT_TOKEN}',
        'Content-Type': 'application/json'
    }
    payload = {
        "datasetId": FastGPTConfig.FASTGPT_DATASET_ID,
        "text": question,
        "limit": limit,
        "similarity": similarity,
        "searchMode": searchMode,  # embedding | fullTextRecall | mixedRecall
        "usingReRank": usingReRank
    }
    # print(payload)
    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status == 200:
                response_message = await response.text()
                response_message = json.loads(response_message)
                # print(response_message)
                if response_message["data"]:
                    knowledge = [{"question":i["q"], "answer":i["a"], "score":i["score"][0]["value"]} for i in response_message["data"]["list"]]
                else:
                    knowledge = []
            else:
                knowledge = []
    return knowledge


if __name__ == '__main__':
    question = "作者是谁？"
    # 运行异步函数
    loop = asyncio.get_event_loop()
    res = loop.run_until_complete(retrieve_knowledge(question))
    print(res)