version: '3.8'

services:
  ai-chat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-chat-app
    ports:
      - "31888:8000"  # 映射到配置文件中的端口
    environment:
      # LLM 配置
      - OPENAI_API_KEY=${OPENAI_API_KEY:-sk-LWOeOppwk9vNtdQMDMKG5UMvtCHQPvGyMEWWfSRJDtYcxOxz}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-http://**************:31889/v1}
      - OPENAI_MODEL=${OPENAI_MODEL:-qwen32b}
      - OPENAI_MAX_TOKENS=${OPENAI_MAX_TOKENS:-30720}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0.7}
      
      # API 配置
      - API_HOST=${API_HOST:-0.0.0.0}
      - API_PORT=${API_PORT:-8000}
      - API_TOKEN=${API_TOKEN:-sk-bU5IdUuSTrEeEpkxB5Lu7Vg7srq7WQkDC58taQq6gQGPWROO}
      
      # 知识库配置
      - FASTGPT_DATASET_ID=${FASTGPT_DATASET_ID:-684baee27bd72143c86d5021}
      - FASTGPT_TOKEN=${FASTGPT_TOKEN:-fastgpt-uM0RzZAYiN1qnCBdx4r6HlI7Y2JdT8jU7zP73gl1NJDRQBSgTvzlrAu}
      - FASTGPT_URL=${FASTGPT_URL:-http://**************:31800/api/core/dataset/searchTest}
      
      # MRP 配置
      - MRP_API_BASE=${MRP_API_BASE:-http://test.jhdms.com/}
      - MRP_APP_SECRET=${MRP_APP_SECRET:-vRJwwdspNhDGiVzRivms}
      - MRP_PRODUCT_SEARCH=${MRP_PRODUCT_SEARCH:-api/jhdms/search}
      - MRP_PRODUCT_INFO=${MRP_PRODUCT_INFO:-api/jhdms/info}
      - MRP_PRODUCT_PRICE=${MRP_PRODUCT_PRICE:-api/jhdms/price}
      - MRP_PRODUCT_STOCK=${MRP_PRODUCT_STOCK:-api/jhdms/stock}
      
      # 日志配置
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_DIR=${LOG_DIR:-logs}
      - LOG_RETENTION_DAYS=${LOG_RETENTION_DAYS:-30}
      - LOG_COMPRESSION=${LOG_COMPRESSION:-zip}
    
    volumes:
      - ./logs:/app/logs  # 持久化日志
    
    restart: unless-stopped
    
    networks:
      - ai-chat-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ai-chat-network:
    driver: bridge
