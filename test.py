#############################################################  mrp api 调用测试


# from ai_mcp.query import get_products


# products = get_products("弓箭")
# print(products)




# ############################################################  mcp 服务调用测试

# import asyncio
# from fastmcp import Client
# from ai_mcp.server import mcp


# async def test_mcp_server():
#     """测试 MCP 服务器的工具功能"""
#     print("开始测试 FastMCP 服务器...")
    
#     # 使用内存传输连接到服务器
#     async with Client(mcp) as client:
#         # 列出可用的工具
#         tools = await client.list_tools()
#         print(f"可用工具: {[tool.name for tool in tools]}")
#         print(tools)
        
#         # 测试搜索产品工具
#         print("\n测试搜索产品功能...")
#         try:
#             result = await client.call_tool("search_products", {"name": "星巴克"})
#             print(f"搜索结果: {result}")
#         except Exception as e:
#             print(f"搜索产品时出错: {e}")

#         # 测试获取产品信息工具
#         print("\n测试获取产品信息功能...")
#         try:
#             result = await client.call_tool("get_product_info_by_id", {"product_id": "0c131617-5d21-41c9-920e-859e18cbce98"})
#             print(f"产品信息: {result}")
#         except Exception as e:
#             print(f"获取产品信息时出错: {e}")

#         # 测试获取库存信息工具
#         print("\n测试获取库存信息功能...")
#         try:
#             result = await client.call_tool("get_product_stock_by_id", {"product_id": "0c131617-5d21-41c9-920e-859e18cbce98"})
#             print(f"库存信息: {result}")
#         except Exception as e:
#             print(f"获取库存信息时出错: {e}")

#         # 测试获取价格信息工具
#         print("\n测试获取价格信息功能...")
#         try:
#             result = await client.call_tool("get_product_price_by_id", {"product_id": "0c131617-5d21-41c9-920e-859e18cbce98"})
#             print(f"价格信息: {result}")
#         except Exception as e:
#             print(f"获取价格信息时出错: {e}")


# if __name__ == "__main__":
#     asyncio.run(test_mcp_server())


######################################################## 知识库测试
import asyncio
from knowledge import retrieve_knowledge


if __name__ == '__main__':
    question = "AABB"
    # 运行异步函数
    loop = asyncio.get_event_loop()
    res = loop.run_until_complete(retrieve_knowledge(question))
    print(res)