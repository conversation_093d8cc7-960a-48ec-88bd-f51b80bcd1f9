# 智能客服系统

智能客服是一个基于自然语言处理和机器学习的机器人，使用大模型作为后端，与客户进行智能问答。

## 接口说明

api 地址：/api/chat，POST方法，兼容OpenAI格式
请求参数：
    user_id: str = ""
    chat_id: str = ""
    msg_id: str = ""
    prompt: str = ""
    messages: Optional[List[ChatMessage]]


返回格式：
    code: int = 200
    message: str = "success"
    data: 
        user_id: str = ""
        chat_id: str = ""
        msg_id: str = ""
        answer_id: str = ""
        success: bool
        reasoning_content: str
        answer: str
        prompt_tokens: int
        completion_tokens: int
        all_tokens: int


# MCP集成智能问答服务实现文档

## 🎯 项目概述

本项目成功实现了基于MCP（Model Context Protocol）的智能问答服务，能够动态获取和使用MCP服务器提供的工具，为用户提供产品查询等功能。

## 🏗️ 架构设计

### 核心组件

1. **MCP服务器** (`ai_mcp/server.py`)
   - 使用FastMCP框架
   - 提供产品查询相关工具
   - 支持动态添加新工具

2. **LLM集成** (`utils/llm.py`)
   - 动态获取MCP工具列表
   - 基于文本解析的工具调用
   - 兼容不支持原生工具调用的LLM服务

3. **智能问答API** (`main.py`)
   - FastAPI框架
   - 支持OpenAI兼容格式
   - 集成工具调用结果展示

## 🔧 核心功能

### 1. 动态工具获取
```python
async def get_available_tools():
    """动态获取MCP服务器提供的工具列表"""
    async with Client(mcp) as client:
        tools = await client.list_tools()
        # 转换为标准格式
        return formatted_tools
```

### 2. 智能工具调用
- **文本解析方式**：`<tool_call>function_name(arg1="value1")</tool_call>`
- **自动参数提取**：支持多参数解析
- **错误处理**：完善的异常处理机制

### 3. 工具结果处理
- **JSON解析**：自动处理MCP返回的TextContent对象
- **结果整合**：将工具结果整合到LLM回答中
- **状态跟踪**：记录工具调用状态和结果

## 📋 可用工具

当前MCP服务器提供以下工具：

1. **search_products** - 产品搜索
2. **get_product_info_by_id** - 产品详情查询
3. **get_product_stock_by_id** - 库存查询
4. **get_product_price_by_id** - 价格查询
5. **get_product_full_info** - 完整信息查询（新增）

## 🚀 扩展性设计

### 添加新工具的步骤

1. **在MCP服务器中添加新工具**：
```python
@mcp.tool
def new_tool_function(param: str) -> Dict[str, Any]:
    """新工具描述"""
    # 实现逻辑
    return {"status": "success", "data": result}
```

2. **无需修改LLM代码**：
   - 系统会自动检测新工具
   - 自动生成工具描述
   - 自动支持工具调用

### 优势

- ✅ **零配置扩展**：添加新API无需修改LLM代码
- ✅ **动态发现**：运行时自动获取工具列表
- ✅ **类型安全**：保持工具定义的一致性
- ✅ **易于维护**：工具定义集中在MCP服务器

## 🧪 测试验证

### 测试脚本

1. **基础功能测试**：`test_mcp_integration.py`
2. **动态工具测试**：`test_dynamic_tools.py`

### 测试结果

- ✅ 产品搜索功能正常
- ✅ 产品详情查询正常
- ✅ 普通对话功能正常
- ✅ 动态工具识别正常
- ✅ 新工具自动集成正常

## 💡 技术亮点

### 1. 兼容性设计
- 支持不具备原生工具调用功能的LLM
- 使用文本解析方式实现工具调用
- 保持与OpenAI格式的兼容性

### 2. 错误处理
- MCP连接失败处理
- 工具调用异常处理
- JSON解析错误处理

### 3. 性能优化
- 异步工具调用
- 连接复用
- 结果缓存机制

## 🔮 未来扩展

1. **更多MRP API集成**
2. **工具调用缓存**
3. **并行工具调用**
4. **工具调用链**
5. **自定义工具权限**

## 📝 使用示例

```python
# 用户询问
"我想找一些星巴克的产品"

# 系统自动调用
<tool_call>search_products(name="星巴克")</tool_call>

# 返回结果
"以下是搜索到的星巴克产品信息：..."
```

这种设计确保了系统的高度可扩展性和维护性，为后续集成更多MRP API奠定了坚实基础。
